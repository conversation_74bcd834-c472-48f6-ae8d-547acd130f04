package com.taobao.mc.aimi.protocol.handlers.impl

import com.google.gson.Gson
import com.google.gson.JsonElement
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.ext.IDE
import com.taobao.mc.aimi.ext.ToastType
import com.taobao.mc.aimi.ext.actions.aimiToolWindow
import com.taobao.mc.aimi.ext.activities.showTutorial
import com.taobao.mc.aimi.ext.editor.DiffStreamService
import com.taobao.mc.aimi.ext.protocol.CopyTextParams
import com.taobao.mc.aimi.ext.protocol.OpenUrlParam
import com.taobao.mc.aimi.ext.protocol.RunCommandParams
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.protocol.handlers.AbstractMessageHandler
import com.taobao.mc.aimi.settings.AIMISettingService
import com.taobao.mc.aimi.settings.IndexUpdateStatus
import com.taobao.mc.aimi.types.MessageTypes
import com.taobao.mc.aimi.types.MessageTypes.ToIDE
import com.taobao.mc.aimi.types.SetLoginInfoParams
import com.taobao.mc.aimi.types.ShowProgressParams
import java.awt.Toolkit
import java.awt.datatransfer.StringSelection

/**
 * 系统操作相关的消息处理器
 * 包括剪贴板操作、命令执行、进度显示、Toast 提示等
 */

/**
 * 获取剪贴板内容处理器
 */
class GetClipboardContentHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetClipboardContent

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val clipboardContent = ide.getClipboardContent()
        respond(clipboardContent)
    }
}

/**
 * 复制文本处理器
 */
class CopyTextHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.CopyText

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val gson = Gson()
        val params = parseParams<CopyTextParams>(dataElement)
        val textToCopy = params.text
        val clipboard = Toolkit.getDefaultToolkit().systemClipboard
        val stringSelection = StringSelection(textToCopy)
        clipboard.setContents(stringSelection, stringSelection)
        respond(null)
    }
}

/**
 * 运行命令处理器
 */
class RunCommandHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.RunCommand

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseKTParams<RunCommandParams>(dataElement)
        val output = ide.runCommand(params.command, params.options)
        respond(output)
    }
}

/**
 * 子进程处理器
 */
class SubprocessHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.Subprocess

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = dataElement.asJsonObject
        val command = params.get("command").asString
        val cwd = params.get("cwd")?.asString
        val subprocess = ide.subprocess(command, cwd)
        respond(subprocess)
    }
}

/**
 * 显示 Toast 处理器
 */
class ShowToastHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.ShowToast

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val jsonArray = dataElement.asJsonArray

        // Get toast type from first element, default to INFO if invalid
        val typeStr = if (jsonArray.size() > 0) jsonArray[0].asString else ToastType.INFO.value
        val type = ToastType.entries.find { it.value == typeStr } ?: ToastType.INFO

        // Get message from second element
        val message = if (jsonArray.size() > 1) jsonArray[1].asString else ""

        // Get remaining elements as otherParams
        val otherParams = if (jsonArray.size() > 2) {
            jsonArray.drop(2).map { it.asString }.toTypedArray()
        } else {
            emptyArray()
        }

        val result = ide.showToast(type, message, *otherParams)
        respond(result)
    }
}

/**
 * 显示进度处理器
 */
class ShowProgressHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.ShowProgress

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val (id, title, progress, status, type) = parseKTParams<ShowProgressParams>(dataElement)
        ide.showProgress(id, title, progress, status, type)
        respond(null)
    }
}

/**
 * 关闭侧边栏处理器
 */
class CloseSidebarHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.CloseSidebar

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        ApplicationManager.getApplication().invokeLater {
            project.aimiToolWindow?.hide()
        }
        respond(null)
    }
}

/**
 * 打开 URL 处理器
 */
class OpenUrlHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.OpenUrl

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val url = parseParams<OpenUrlParam>(dataElement)
        ide.openUrl(url)
        respond(null)
    }
}

/**
 * 切换开发工具处理器
 */
class ToggleDevToolsHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.ToggleDevTools

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        aimiService.activeBrowser?.openDevtools()
        respond(null)
    }
}

/**
 * 显示教程处理器
 */
class ShowTutorialHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.ShowTutorial

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        showTutorial(project)
        respond(null)
    }
}

/**
 * 索引进度处理器
 */
class IndexProgressHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.IndexProgress

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseKTParams<IndexUpdateStatus>(dataElement)
        logger.info("收到 indexProgress, params: $params")
        val settings = AIMISettingService.instance
        settings.state.indexStatus = params
        // 发布状态变更通知，通知所有监听器更新UI
        settings.notifySettingsUpdated()
        respond(null)
    }
}

/**
 * 获取登录信息处理器
 */
class GetLoginInfoHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetLoginInfo

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        // 这里可以从配置或服务中获取用户登录信息
        val settings = AIMISettingService.instance
        val userInfo = settings.state.userInfo
        // logger.debug("获取登录信息, userInfo: $userInfo")
        respond(userInfo)
    }
}

/**
 * 设置登录信息处理器
 */
class SetLoginInfoHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.SetLoginInfo

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseKTParams<SetLoginInfoParams>(dataElement)
        logger.debug("设置登录信息, userInfo: ${params.user}")

        // 保存用户信息到设置中
        val settings = AIMISettingService.instance
        settings.updateUserInfo(params.user)
        respond(null)

        // 将这个信息同步到Core
        val coreMessenger = aimiService.awaitCoreMessenger()
        coreMessenger.request(
            MessageTypes.ToCore.AuthUpdate,
            mapOf("user" to params.user),
            messageId,
        )
    }
}