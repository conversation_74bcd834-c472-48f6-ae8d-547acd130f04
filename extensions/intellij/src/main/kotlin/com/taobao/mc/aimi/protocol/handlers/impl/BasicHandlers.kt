package com.taobao.mc.aimi.protocol.handlers.impl

import com.google.gson.JsonElement
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.ext.IDE
import com.taobao.mc.aimi.ext.editor.DiffStreamService
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.protocol.handlers.AbstractMessageHandler
import com.taobao.mc.aimi.types.MessageTypes.ToIDE

/**
 * 基础信息相关的消息处理器
 * 包括 IDE 信息、设置、唯一 ID 等基础功能
 */

/**
 * 获取IDE设置处理器
 */
class GetIdeSettingsHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetIdeSettings

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val settings = ide.getIdeSettings()
        respond(settings)
    }
}

/**
 * 获取IDE信息处理器
 */
class GetIdeInfoHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetIdeInfo

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val ideInfo = ide.getIdeInfo()
        respond(ideInfo)
    }
}

/**
 * 获取唯一ID处理器
 */
class GetUniqueIdHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetUniqueId

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val uniqueId = ide.getUniqueId()
        respond(uniqueId)
    }
}

/**
 * 判断工作空间是否远程处理器
 */
class IsWorkspaceRemoteHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.IsWorkspaceRemote

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val isRemote = ide.isWorkspaceRemote()
        respond(isRemote)
    }
}