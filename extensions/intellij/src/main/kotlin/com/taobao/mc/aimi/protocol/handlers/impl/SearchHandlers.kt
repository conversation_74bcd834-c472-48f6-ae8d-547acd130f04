package com.taobao.mc.aimi.protocol.handlers.impl

import com.google.gson.JsonElement
import com.intellij.ide.DataManager
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.readAction
import com.intellij.openapi.components.service
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.ext.IDE
import com.taobao.mc.aimi.ext.QuerySymbolInfoParams
import com.taobao.mc.aimi.ext.actions.aimiToolWindow
import com.taobao.mc.aimi.ext.editor.DiffStreamService
import com.taobao.mc.aimi.ext.protocol.GetFileResultsParams
import com.taobao.mc.aimi.ext.protocol.GetSearchResultsParams
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.protocol.handlers.AbstractMessageHandler
import com.taobao.mc.aimi.psi.ObjectTypeResolver
import com.taobao.mc.aimi.services.search.SymbolSearchService
import com.taobao.mc.aimi.types.MessageTypes.ToIDE
import com.taobao.mc.aimi.util.mSelectedTextEditor
import kotlin.time.measureTimedValue

/**
 * 搜索相关的消息处理器
 * 包括搜索结果、文件结果、符号查询等
 */

/**
 * 获取搜索结果处理器
 */
class GetSearchResultsHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetSearchResults

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<GetSearchResultsParams>(dataElement)
        val results = ide.getSearchResults(params.query, params.maxResults)
        respond(results)
    }
}

/**
 * 获取文件结果处理器
 */
class GetFileResultsHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetFileResults

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<GetFileResultsParams>(dataElement)
        val results = ide.getFileResults(params.pattern, params.maxResults)
        respond(results)
    }
}

/**
 * 查询符号信息处理器
 */
class QuerySymbolInfoHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.QuerySymbolInfo

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val symbolInfos = readAction {
            val editorManager = FileEditorManager.getInstance(project)
            editorManager.mSelectedTextEditor?.let { textEditor ->
                val resolver = project.service<ObjectTypeResolver>()
                val (objectInfos, time) = measureTimedValue {
                    resolver.resolveObjectAtCursor(textEditor)
                }
                logger.debug("Search symbols cost: ${time.inWholeMilliseconds}, results: ${objectInfos.size}")
                objectInfos
                    .distinctBy { it.qualifiedType }
                    .filterNot { it.qualifiedType == "kotlin.Any" || it.qualifiedType == "java.lang.Object" }
                    .map { it.toSymbolInfo(project) }
            }
        }
        if (symbolInfos != null) {
            respond(symbolInfos)
            return
        }
        val params = parseKTParams<QuerySymbolInfoParams>(dataElement)
        // 获取当前行光标前面的内容
        val searchPattern = runCatching { params.query.take(params.cursorIndex) }.getOrElse { params.query }
        val toolWindow = project.aimiToolWindow
        val dataContext = DataManager.getInstance().getDataContext(toolWindow!!.component)
        val anActionEvent = AnActionEvent.createFromDataContext("", null, dataContext)
        val searchService = project.service<SymbolSearchService>()
        val searchSymbols = searchService.searchSymbols(
            project,
            params.query,
            params.cursorIndex,
            anActionEvent
        )
        respond(searchSymbols)
    }
}
