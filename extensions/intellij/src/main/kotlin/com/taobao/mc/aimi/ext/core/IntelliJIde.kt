package com.taobao.mc.aimi.ext.core

import com.intellij.codeInsight.daemon.impl.HighlightInfo
import com.intellij.execution.configurations.GeneralCommandLine
import com.intellij.execution.util.ExecUtil
import com.intellij.ide.BrowserUtil
import com.intellij.ide.plugins.PluginManagerCore
import com.intellij.lang.annotation.HighlightSeverity
import com.intellij.notification.NotificationAction
import com.intellij.notification.NotificationGroupManager
import com.intellij.notification.NotificationType
import com.intellij.notification.NotificationsManager
import com.intellij.openapi.application.ApplicationInfo
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.EDT
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.impl.DocumentMarkupModel
import com.intellij.openapi.extensions.PluginId
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.progress.util.ProgressWindow
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.guessProjectDir
import com.intellij.psi.PsiDocumentManager
import com.intellij.testFramework.LightVirtualFile
import com.taobao.mc.aimi.execution.TerminalResult
import com.taobao.mc.aimi.execution.executeTerminal
import com.taobao.mc.aimi.ext.*
import com.taobao.mc.aimi.ext.constants.AIMIConstants
import com.taobao.mc.aimi.ext.constants.getAIMIGlobalPath
import com.taobao.mc.aimi.ext.protocol.TerminalOptions
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.ext.utils.*
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.services.ClipboardManagerService
import com.taobao.mc.aimi.services.ProgressService
import com.taobao.mc.aimi.settings.AIMISettingService
import com.taobao.mc.aimi.types.ProgressType
import com.taobao.mc.aimi.util.*
import kotlinx.coroutines.*
import java.awt.Toolkit
import java.awt.datatransfer.DataFlavor
import java.io.File

class IntelliJIDE(
    private val project: Project,
    private val continuePluginService: AIMIPluginService,

    ) : IDE {
    private val logger = LoggerManager.getLogger(javaClass)

    private val gitService by lazy { project.service<GitService>() }
    private val fileUtils = FileUtils()

    private val ripgrep: String = getRipgrepPath()

    init {
        try {
            val os = getOS()

            if (os == OS.LINUX || os == OS.MAC) {
                val file = File(ripgrep)
                if (!file.canExecute()) {
                    file.setExecutable(true)
                }
            }
        } catch (e: Throwable) {
            logger.warn("Exception occurred", e)
        }
    }

    override suspend fun getIdeInfo(): IdeInfo {
        val applicationInfo = ApplicationInfo.getInstance()
        val ideName: String = applicationInfo.fullApplicationName
        val ideVersion = applicationInfo.fullVersion
        val sshClient = System.getenv("SSH_CLIENT")
        val sshTty = System.getenv("SSH_TTY")

        var remoteName = "local"
        if (sshClient != null || sshTty != null) {
            remoteName = "ssh"
        }

        val pluginId = AIMIConstants.PLUGIN_ID
        val plugin = PluginManagerCore.getPlugin(PluginId.getId(pluginId))
        val extensionVersion = plugin?.version ?: "Unknown"

        return IdeInfo(
            ideType = "jetbrains",
            name = ideName,
            version = ideVersion,
            remoteName = remoteName,
            extensionVersion = extensionVersion,
            isPrerelease = false // TODO: Implement prerelease detection for JetBrains
        )
    }

    override suspend fun getIdeSettings(): IdeSettings {
        val settings = service<AIMISettingService>()


        return IdeSettings(
            remoteConfigServerUrl = "",
            remoteConfigSyncPeriod = 60,
            userToken = "",
            continueTestEnvironment = settings.state.environment,
            pauseCodebaseIndexOnStart = false, // TODO: Needs to be implemented
        )
    }

    override suspend fun getDiff(includeUnstaged: Boolean): List<String> {
        return gitService.getDiff(includeUnstaged)
    }

    override suspend fun getClipboardContent(): Map<String, Any?> {
        val data = withContext(Dispatchers.IO) {
            runCatching {
                val clipboard = Toolkit.getDefaultToolkit().systemClipboard
                clipboard.getData(DataFlavor.stringFlavor)
            }.getOrNull()
        }
        val text = data as? String ?: ""
        val clipboardService = ClipboardManagerService.getInstance(project)
        val lastRIF = clipboardService.lastRIF
        val lastContent = clipboardService.lastContent
        val lastCopiedTIme = clipboardService.lastCopiedTime

        // perform values not null
        return mapOf(
            "text" to (lastRIF?.contents ?: lastContent ?: text),
            "copiedAt" to lastCopiedTIme,
            "filepath" to lastRIF?.filepath,
            "range" to lastRIF?.range,
        )
    }

    override suspend fun isTelemetryEnabled(): Boolean {
        return true
    }

    override suspend fun isWorkspaceRemote(): Boolean {
        return this.getIdeInfo().remoteName != "local"
    }

    override suspend fun getUniqueId(): String {
        return getMachineUniqueID()
    }

    override suspend fun getTerminalContents(): String {
        /*return withContext(Dispatchers.EDT) {
            try {
                val toolWindow = ToolWindowManager.getInstance(project).getToolWindow("Terminal")

                val terminalView = TerminalView.getInstance(project)
                // Find the first terminal widget selected, whatever its state, running command or not.
                val widget = terminalView.getWidgets().filterIsInstance<ShellTerminalWidget>().firstOrNull {
                    toolWindow?.getContentManager()?.getContent(it)?.isSelected ?: false
                }

                if (widget != null) {
                    val textBuffer = widget.terminalTextBuffer
                    val stringBuilder = StringBuilder()
                    // Iterate through all lines in the buffer (history + screen)
                    for (i in 0 until textBuffer.historyLinesCount + textBuffer.screenLinesCount) {
                        stringBuilder.append(textBuffer.getLine(i).text).append('\n')
                    }
                    stringBuilder.toString()
                } else {
                    "" // Return empty if no terminal is available
                }
            } catch (e: Exception) {
                println("Error getting terminal contents: ${e.message}")
                e.printStackTrace()
                "" // Return empty on error
            }
        }*/
        return ""
    }

    override suspend fun getDebugLocals(threadIndex: Int): String {
        throw NotImplementedError("getDebugLocals not implemented yet")
    }

    override suspend fun getTopLevelCallStackSources(threadIndex: Int, stackDepth: Int): List<String> {
        throw NotImplementedError("getTopLevelCallStackSources not implemented")
    }

    override suspend fun getAvailableThreads(): List<Thread> {
        throw NotImplementedError("getAvailableThreads not implemented yet")
    }

    override suspend fun getWorkspaceDirs(): List<String> {
        return workspaceDirectories().toList()
    }

    override suspend fun fileExists(filepath: String): Boolean =
        fileUtils.fileExists(filepath)

    override suspend fun writeFile(path: String, contents: String) =
        withContext(Dispatchers.EDT) {
            fileUtils.writeFile(path, contents, project)
        }

    override suspend fun deleteFile(path: String) = fileUtils.deleteFile(project, path)

    override suspend fun showVirtualFile(title: String, contents: String) {
        val virtualFile = LightVirtualFile(title, contents)
        ApplicationManager.getApplication().invokeLater {
            FileEditorManager.getInstance(project).openFile(virtualFile, true)
        }
    }

    override suspend fun getAIMIDir(): String {
        return getAIMIGlobalPath()
    }

    override suspend fun openFile(path: String) {
        // Convert URI path to absolute file path
        // val uri = UriUtils.parseUri(path)
        // Find the file using the absolute path
        val file = withContext(Dispatchers.IO) {
            UriUtils.uriToVirtualFile(path)
        }

        file?.let {
            ApplicationManager.getApplication().invokeLater {
                FileEditorManager.getInstance(project).openFile(it, true)
            }
        }
    }

    override suspend fun openUrl(url: String) {
        withContext(Dispatchers.IO) {
            BrowserUtil.browse(url)
        }
    }

    override suspend fun runCommand(command: String, options: TerminalOptions): TerminalResult {
        return runCatching {
            executeTerminal(project, command, options)
        }.getOrElse {
            logger.warn("Exception occurred", it)
            TerminalResult("Failed to execute command: $command", true, 1, -1)
        }
    }

    override suspend fun saveFile(filepath: String) {
        ApplicationManager.getApplication().invokeLater {
            val file = UriUtils.uriToVirtualFile(filepath) ?: return@invokeLater
            val fileDocumentManager = FileDocumentManager.getInstance()
            val document = fileDocumentManager.getDocument(file)

            document?.let {
                fileDocumentManager.saveDocument(it)
            }
        }
    }

    override suspend fun readFile(filepath: String): String =
        fileUtils.readFile(filepath)

    override suspend fun readRangeInFile(filepath: String, range: Range): String {
        val fullContents = readFile(filepath)
        val lines = fullContents.lines()
        val startLine = range.start.line
        val startCharacter = range.start.character
        val endLine = range.end.line
        val endCharacter = range.end.character

        val firstLine = lines.getOrNull(startLine)?.substring(startCharacter) ?: ""
        val lastLine = lines.getOrNull(endLine)?.substring(0, endCharacter) ?: ""
        val betweenLines = if (endLine - startLine > 1) {
            lines.subList(startLine + 1, endLine).joinToString("\n")
        } else {
            ""
        }

        return listOf(firstLine, betweenLines, lastLine).filter { it.isNotEmpty() }.joinToString("\n")
    }

    override suspend fun showLines(filepath: String, startLine: Int, endLine: Int) {
        setFileOpen(filepath, true)
        project.showLines(filepath, startLine, endLine)
    }

    override suspend fun showDiff(filepath: String, newContents: String, stepIndex: Int) {
        continuePluginService.diffManager?.showDiff(filepath, newContents, stepIndex)
    }

    override suspend fun getOpenFiles(): List<String> =
        withContext(Dispatchers.EDT) {
            FileEditorManager.getInstance(project).openFiles
                .mapNotNull { it.toUriOrNull() }
                .toList()
        }

    override suspend fun getCurrentFile(): Map<String, Any>? =
        withContext(Dispatchers.EDT) {
            val fileEditorManager = FileEditorManager.getInstance(project)
            val document = fileEditorManager.mSelectedTextEditor?.document
            val virtualFile = document?.let { FileDocumentManager.getInstance().getFile(it) }
            virtualFile?.toUriOrNull()?.let {
                mapOf(
                    "path" to it,
                    "contents" to document.text,
                    "isUntitled" to false
                )
            }
        }

    override suspend fun getPinnedFiles(): List<String> {
        // Returning open files for now as per existing code
        return getOpenFiles()
    }

    override suspend fun getFileResults(pattern: String, maxResults: Int?): List<String> {
        val ideInfo = this.getIdeInfo()
        if (ideInfo.remoteName == "local") {
            try {
                val command = GeneralCommandLine(
                    ripgrep,
                    "--files",
                    "--iglob",
                    pattern
                )
                if (maxResults != null) {
                    command.withParameters("--max-count", maxResults.toString())
                }

                // 检查忽略文件是否存在再添加参数
                addArgsBeforeFileExists(command)

                command.setWorkDirectory(project.basePath)
                return ExecUtil.execAndGetOutput(command).stdoutLines
            } catch (exception: Exception) {
                val message = "Error executing ripgrep: ${exception.message}"
                showToast(ToastType.ERROR, message)
                return emptyList()
            }
        } else {
            throw NotImplementedError("Ripgrep not supported, this workspace is remote")
        }
    }

    override suspend fun getSearchResults(query: String, maxResults: Int?): String {
        val ideInfo = this.getIdeInfo()
        if (ideInfo.remoteName == "local") {
            try {
                val command = GeneralCommandLine(
                    ripgrep,
                    "-i",
                    "-C",
                    "2",
                    "--heading",
                    "-e",
                    query,
                    "."
                )
                if (maxResults != null) {
                    command.withParameters("--max-count", maxResults.toString())
                }

                // 检查忽略文件是否存在再添加参数
                addArgsBeforeFileExists(command)

                command.setWorkDirectory(project.basePath)
                return ExecUtil.execAndGetOutput(command).stdout
            } catch (exception: Exception) {
                val message = "Error executing ripgrep: ${exception.message}"
                showToast(ToastType.ERROR, message)
                return "Error: Unable to execute ripgrep command."
            }
        } else {
            throw NotImplementedError("Ripgrep not supported, this workspace is remote")
        }
    }


    override suspend fun subprocess(command: String, cwd: String?): List<Any> {
        return withContext(Dispatchers.IO) {
            try {
                val commandList = command.split(" ")
                val commandLine = GeneralCommandLine(commandList)
                    .withWorkDirectory(UriUtils.uriToFileSafe(cwd))

                val output = ExecUtil.execAndGetOutput(commandLine)
                listOf(output.stdout, output.stderr)
            } catch (e: Exception) {
                listOf("", "Error: ${e.message}")
            }
        }
    }

    override suspend fun getProblems(filepath: String?): List<Problem> {
        val problems = mutableListOf<Problem>()

        ApplicationManager.getApplication().invokeAndWait {
            val fileEditorManager = FileEditorManager.getInstance(project)
            val virtualFile = filepath?.let { UriUtils.uriToVirtualFile(filepath) }
            val editor = virtualFile?.let {
                // 如果文件没打开, 就打开, 否则拿不到 problems
                fileEditorManager.openFile(virtualFile, false, false)
                fileEditorManager.getSelectedEditor(virtualFile)?.editor
            } ?: fileEditorManager.mSelectedTextEditor ?: return@invokeAndWait

            val document = editor.document
            val psiFile = PsiDocumentManager.getInstance(project).getPsiFile(document) ?: return@invokeAndWait
            val highlightInfos = DocumentMarkupModel.forDocument(document, project, true)
                .allHighlighters
                .mapNotNull(HighlightInfo::fromRangeHighlighter)

            highlightInfos
                .filter { it.severity == HighlightSeverity.ERROR }
                .forEach { highlightInfo ->
                    val startOffset = highlightInfo.startOffset
                    val endOffset = highlightInfo.endOffset

                    val startLineNumber = document.getLineNumber(startOffset)
                    val endLineNumber = document.getLineNumber(endOffset)
                    val startCharacter = startOffset - document.getLineStartOffset(startLineNumber)
                    val endCharacter = endOffset - document.getLineStartOffset(endLineNumber)

                    problems.add(
                        Problem(
                            filepath = psiFile.virtualFile?.toUriOrNull() ?: "",
                            range = Range(
                                start = Position(
                                    line = startLineNumber,
                                    character = startCharacter
                                ),
                                end = Position(
                                    line = endLineNumber,
                                    character = endCharacter
                                )
                            ),
                            message = highlightInfo.description
                        )
                    )
                }
        }

        return problems
    }

    override suspend fun getBranch(dir: String): String {
        return withContext(Dispatchers.IO) {
            try {
                val directory = UriUtils.uriToFileSafe(dir)
                    ?: UriUtils.uriToFileSafe(workspaceDirectories().firstOrNull())
                    ?: return@withContext "NONE"
                val targetDir = if (directory.isFile) directory.parentFile else directory
                val commandLine = GeneralCommandLine("git", "rev-parse", "--abbrev-ref", "HEAD")
                    .withWorkDirectory(targetDir)
                return@withContext ExecUtil.execAndGetOutput(commandLine).stdout.trim().ifEmpty { "NONE" }
            } catch (e: Exception) {
                "NONE"
            }
        }
    }

    override suspend fun getTags(artifactId: String): List<IndexTag> {
        val workspaceDirs = this.getWorkspaceDirs()

        // Collect branches concurrently using Kotlin coroutines
        val branches = withContext(Dispatchers.IO) {
            workspaceDirs.map { dir ->
                async { getBranch(dir) }
            }.map { it.await() }
        }

        // Create the list of IndexTag objects
        return workspaceDirs.mapIndexed { index, directory ->
            IndexTag(artifactId, branches[index], directory)
        }
    }

    override suspend fun getRepoName(dir: String): String? {
        return withContext(Dispatchers.IO) {
            val directory = UriUtils.uriToFileSafe(dir)
                ?: UriUtils.uriToFileSafe(workspaceDirectories().firstOrNull())
                ?: return@withContext null
            val targetDir = if (directory.isFile) directory.parentFile else directory
            runCatching {
                val commandLine = GeneralCommandLine("git", "config", "--get", "remote.origin.url")
                    .withWorkDirectory(targetDir)
                ExecUtil.execAndGetOutput(commandLine).stdout.trim()
            }.getOrNull()
        }
    }

    override suspend fun showToast(type: ToastType, message: String, vararg otherParams: Any): Any {
        return withContext(Dispatchers.Default) {
            val notificationType = when (type) {
                ToastType.ERROR -> NotificationType.ERROR
                ToastType.WARNING -> NotificationType.WARNING
                else -> NotificationType.INFORMATION
            }

            val deferred = CompletableDeferred<String?>()
            val icon = AIMIIcons.AIMI

            val notification = NotificationGroupManager.getInstance().getNotificationGroup(AIMIConstants.PLUGIN_NAME)
                .createNotification(message, notificationType).setIcon(icon)

            if (notificationType == NotificationType.ERROR) {
                notification.isImportant = true
                notification.isImportantSuggestion = true
                notification.isSuggestionType = true
            }

            val buttonTexts = otherParams.filterIsInstance<String>().toTypedArray()
            buttonTexts.forEach { buttonText ->
                notification.addAction(NotificationAction.create(buttonText) { _, _ ->
                    deferred.complete(buttonText)
                    notification.expire()
                })
            }

            val job = launch(Dispatchers.IO) {
                delay(15000)
                if (!deferred.isCompleted) {
                    deferred.complete(null)
                    notification.expire()
                }
            }

            notification.whenExpired {
                if (!deferred.isCompleted) {
                    deferred.complete(null)
                }
            }

            NotificationsManager.getNotificationsManager().showNotification(notification, project)
            // notification.notify(project)

            val text = deferred.await() ?: ""
            job.cancel()
            text
        }
    }

    override suspend fun showProgress(
        id: String,
        title: String,
        progress: Double,
        status: String,
        type: ProgressType
    ) {
        try {
            val progressService = ProgressService.getInstance(project)
            val indicator = progressService.getProgressIndicator(id) ?: run {
                when (type) {
                    ProgressType.Apply, ProgressType.Chat -> {
                        val progressWindow = ProgressWindow(false, false, project)
                        progressWindow.title = title
                        progressWindow.isIndeterminate = true
                        progressWindow.start()
                        progressService.addProgressIndicator(id, progressWindow)
                    }
                }
            }
            indicator.fraction = progress.coerceIn(0.0, 1.0)
            if (progress == 1.0 || status == "done" || status == "error") {
                indicator.stop()
                progressService.removeProgressIndicator(id)
                return
            }
        } catch (e: Exception) {
            logger.warn("显示进度时发生异常", e)
        }
    }

    override suspend fun getGitRootPath(dir: String): String? {
        return withContext(Dispatchers.IO) {
            val directory = UriUtils.uriToFileSafe(dir) ?: return@withContext null
            val commandLine = GeneralCommandLine("git", "rev-parse", "--show-toplevel")
                .withWorkDirectory(directory)
            runCatching {
                ExecUtil.execAndGetOutput(commandLine).stdout.trim()
            }.getOrNull()
        }
    }

    override suspend fun listDir(dir: String): List<List<Any>> =
        fileUtils.listDir(dir)

    override suspend fun getFileStats(files: List<String>): Map<String, FileStats> {
        return files.associateWith { UriUtils.uriToFileSafe(it) }
            .mapNotNull {
                val file = it.value ?: return@mapNotNull null
                it.key to FileStats(file.lastModified(), file.length())
            }
            .toMap()
    }

    override suspend fun gotoDefinition(location: Location): List<RangeInFile> {
        throw NotImplementedError("gotoDefinition not implemented yet")
    }

    override suspend fun gotoTypeDefinition(location: Location): List<RangeInFile> {
        throw NotImplementedError("gotoTypeDefinition not implemented yet")
    }

    override suspend fun getSignatureHelp(location: Location): SignatureHelp? {
        throw NotImplementedError("getSignatureHelp not implemented yet")
    }

    override suspend fun getReferences(location: Location): List<RangeInFile> {
        throw NotImplementedError("getReferences not implemented yet")
    }

    override suspend fun getDocumentSymbols(textDocumentIdentifier: String): List<DocumentSymbol> {
        throw NotImplementedError("getDocumentSymbols not implemented yet")
    }

    override fun onDidChangeActiveTextEditor(callback: (filepath: String) -> Unit) {
        throw NotImplementedError("onDidChangeActiveTextEditor not implemented yet")
    }

    private fun setFileOpen(filepath: String, open: Boolean = true) {
        val file = UriUtils.uriToVirtualFile(filepath)

        file?.let {
            if (open) {
                ApplicationManager.getApplication().invokeLater {
                    FileEditorManager.getInstance(project).openFile(it, true)
                }
            } else {
                ApplicationManager.getApplication().invokeLater {
                    FileEditorManager.getInstance(project).closeFile(it)
                }
            }
        }
    }

    private fun workspaceDirectories(): Array<String> {
        val dirs = this.continuePluginService.workspacePaths

        if (dirs?.isNotEmpty() == true) {
            return dirs
        }

        return listOfNotNull(project.guessProjectDir()?.toUriOrNull()).toTypedArray()
    }

    private fun addArgsBeforeFileExists(command: GeneralCommandLine) {
        // 检查忽略文件是否存在再添加参数
        val basePath = project.basePath
        if (basePath != null) {
            val aimiIgnoreFile = File(basePath, ".aimi_ignore")
            if (aimiIgnoreFile.exists()) {
                command.withParameters("--ignore-file", ".aimi_ignore")
            }

            val gitignoreFile = File(basePath, ".gitignore")
            if (gitignoreFile.exists()) {
                command.withParameters("--ignore-file", ".gitignore")
            }
        }
    }
}
