package com.taobao.mc.aimi.protocol.handlers

import com.google.gson.JsonElement
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.ext.IDE
import com.taobao.mc.aimi.ext.editor.DiffStreamService
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.types.MessageTypes.ToIDE
import com.taobao.mc.aimi.util.kGson
import com.taobao.mc.aimi.util.kJson
import kotlinx.serialization.Serializable

/**
 * 消息处理器接口
 * 定义了处理特定类型消息的策略
 */
interface MessageHandler {
    /**
     * 处理消息
     * @param dataElement 消息数据
     * @param respond 响应回调函数
     */
    suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit)

    /**
     * 获取支持的消息类型
     */
    val supportedMessageType: ToIDE
}

/**
 * 抽象消息处理器基类
 * 提供通用的依赖注入和工具方法
 *
 * 注意：异常处理已统一在 IdeProtocolClient#handleMessage 中实现，
 * 各个 handler 实现类不需要再单独处理异常
 */
abstract class AbstractMessageHandler(
    protected val project: Project,
    protected val ide: IDE,
    protected val aimiService: AIMIPluginService,
    protected val diffStreamService: DiffStreamService
) : MessageHandler {

    protected val logger = LoggerManager.getLogger(javaClass)

    /**
     * 解析 JSON 参数
     */
    protected inline fun <reified T> parseParams(dataElement: JsonElement): T {
        return kGson.fromJson(dataElement, T::class.java)
    }


    /**
     * 解析 JSON 参数
     */
    protected inline fun <reified T> parseKTParams(dataElement: JsonElement): T where T : @Serializable Any {
        return kJson.decodeFromString(dataElement.toString())
    }

}
