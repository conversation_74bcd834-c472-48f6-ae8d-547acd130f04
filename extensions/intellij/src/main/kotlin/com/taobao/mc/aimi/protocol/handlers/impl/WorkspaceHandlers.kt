package com.taobao.mc.aimi.protocol.handlers.impl

import com.google.gson.JsonElement
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.ext.IDE
import com.taobao.mc.aimi.ext.editor.DiffStreamService
import com.taobao.mc.aimi.ext.protocol.GetFileStatsParams
import com.taobao.mc.aimi.ext.protocol.ListDirParams
import com.taobao.mc.aimi.ext.protocol.getTagsParams
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.protocol.handlers.AbstractMessageHandler
import com.taobao.mc.aimi.types.GetProblemsParams
import com.taobao.mc.aimi.types.MessageTypes.ToIDE

/**
 * 工作区和项目相关的消息处理器
 * 包括工作区目录、配置、终端内容等
 */

/**
 * 获取工作区目录处理器
 */
class GetWorkspaceDirsHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetWorkspaceDirs

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val workspaceDirs = ide.getWorkspaceDirs()
        respond(workspaceDirs)
    }
}

/**
 * 获取终端内容处理器
 */
class GetTerminalContentsHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetTerminalContents

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val terminalContents = ide.getTerminalContents()
        respond(terminalContents)
    }
}

/**
 * 获取标签处理器
 */
class GetTagsHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetTags

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val artifactId = parseParams<getTagsParams>(dataElement)
        val tags = ide.getTags(artifactId)
        respond(tags)
    }
}

/**
 * 获取问题处理器
 */
class GetProblemsHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetProblems

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseKTParams<GetProblemsParams>(dataElement)
        val problems = ide.getProblems(params.filepath)
        respond(problems)
    }
}

/**
 * 列出目录处理器
 */
class ListDirHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.ListDir

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<ListDirParams>(dataElement)
        val dirContents = ide.listDir(params.dir)
        respond(dirContents)
    }
}

/**
 * 获取文件统计处理器
 */
class GetFileStatsHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetFileStats

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<GetFileStatsParams>(dataElement)
        val fileStats = ide.getFileStats(params.files)
        respond(fileStats)
    }
}
