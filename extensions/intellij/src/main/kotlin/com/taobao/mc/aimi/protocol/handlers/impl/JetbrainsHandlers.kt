package com.taobao.mc.aimi.protocol.handlers.impl

import com.google.gson.JsonElement
import com.intellij.openapi.project.Project
import com.intellij.util.application
import com.taobao.mc.aimi.ext.ChangeTitleParams
import com.taobao.mc.aimi.ext.IDE
import com.taobao.mc.aimi.ext.core.GetTheme
import com.taobao.mc.aimi.ext.editor.DiffStreamService
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.ext.utils.getMachineUniqueID
import com.taobao.mc.aimi.protocol.handlers.AbstractMessageHandler
import com.taobao.mc.aimi.settings.AIMISettingService
import com.taobao.mc.aimi.types.ChangeWindowParams
import com.taobao.mc.aimi.types.GetShortcutsParams
import com.taobao.mc.aimi.types.MessageTypes.ToIDE
import com.taobao.mc.aimi.types.ShortcutType
import com.taobao.mc.aimi.util.ShortcutUtils

/**
 * JetBrains 特定功能的消息处理器
 * 包括 OSR 设置、颜色主题、窗口管理等 JetBrains IDE 特有功能
 */

/**
 * JetBrains OSR 启用状态处理器
 */
class JetbrainsIsOSREnabledHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.JetbrainsIsOSREnabled

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val isOSREnabled = application.getService(AIMISettingService::class.java).state.enableOSR
        respond(isOSREnabled)
    }
}

/**
 * JetBrains 获取颜色主题处理器
 */
class JetbrainsGetColorsHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.JetbrainsGetColors

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val colors = GetTheme().getTheme()
        respond(colors)
    }
}

/**
 * JetBrains 加载时处理器
 */
class JetbrainsOnLoadHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.JetbrainsOnLoad

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val jsonData = mutableMapOf(
            "windowId" to aimiService.windowId,
            "workspacePaths" to aimiService.workspacePaths,
            "vscMachineId" to getMachineUniqueID(),
            "vscMediaUrl" to "http://continue",
        )
        respond(jsonData)
    }
}

/**
 * JetBrains 更改标题处理器
 */
class JetbrainsChangeTitleHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.JetbrainsChangeTitle

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val (title) = parseKTParams<ChangeTitleParams>(dataElement)
        aimiService.changeTitle(title)
        respond(null)
    }
}

/**
 * JetBrains 更改窗口处理器
 */
class JetbrainsChangeWindowHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.JetbrainsChangeWindow

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val (type, url) = parseKTParams<ChangeWindowParams>(dataElement)
        aimiService.changeWindow(type, url)
        respond(null)
    }
}

/**
 * 查询切换 AIMI 快捷键处理器
 */
class JetbrainsToggleAIMIShortcutsHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.JetbrainsGetShortcuts

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseKTParams<GetShortcutsParams>(dataElement)
        val shortcuts = when (params.type) {
            ShortcutType.ToggleChat -> {
                ShortcutUtils.getShortcutsForAction("aimi.toggleAIMI")
            }
        }
        respond(shortcuts)
    }
}