package com.taobao.mc.aimi.protocol.handlers.impl

import com.google.gson.JsonElement
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.ext.IDE
import com.taobao.mc.aimi.ext.editor.DiffStreamService
import com.taobao.mc.aimi.ext.protocol.GetBranchParams
import com.taobao.mc.aimi.ext.protocol.GetDiffParams
import com.taobao.mc.aimi.ext.protocol.GetGitRootPathParams
import com.taobao.mc.aimi.ext.protocol.GetRepoNameParams
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.protocol.handlers.AbstractMessageHandler
import com.taobao.mc.aimi.types.MessageTypes.ToIDE

/**
 * Git 版本控制相关的消息处理器
 * 包括获取 Git 根路径、分支、仓库名、差异等
 */

/**
 * 获取 Git 根路径处理器
 */
class GetGitRootPathHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetGitRootPath

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<GetGitRootPathParams>(dataElement)
        val gitRootPath = ide.getGitRootPath(params.dir)
        respond(gitRootPath)
    }
}

/**
 * 获取分支处理器
 */
class GetBranchHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetBranch

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<GetBranchParams>(dataElement)
        val branch = ide.getBranch(params.dir)
        respond(branch)
    }
}

/**
 * 获取仓库名处理器
 */
class GetRepoNameHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetRepoName

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<GetRepoNameParams>(dataElement)
        val repoName = ide.getRepoName(params.dir)
        respond(repoName)
    }
}

/**
 * 获取差异处理器
 */
class GetDiffHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetDiff

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<GetDiffParams>(dataElement)
        val diff = ide.getDiff(params.includeUnstaged)
        respond(diff)
    }
}
