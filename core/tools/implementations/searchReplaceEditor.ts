import {
  inferResolvedUriFromRelativePath,
  resolveRelativePathInDir,
} from "../../util/ideUtils";
import { ToolImpl } from ".";
import { getStringArg } from "../parseArgs";
import type { ContextItem, ToolExtras } from "../..";

// File history storage - in a real implementation this might be persisted
interface FileOperation {
  command: "create" | "str_replace" | "insert";
  path: string;
  content: string;
  fileDescriptor: string;
  insertLine?: number;
}
const fileHistory: Map<string, FileOperation[]> = new Map();

const TRUNCATED_MESSAGE =
  "To save on context only part of this file has been shown to you. You should retry this tool after you have searched inside the file with `grep -n` in order to find the line numbers of what you are looking for.";
const MAX_RESPONSE_LEN = 16000;
const SNIPPET_LINES = 4;

function maybeTruncate(
  content: string,
  truncateAfter: number = MAX_RESPONSE_LEN,
): string {
  return content.length <= truncateAfter
    ? content
    : content.substring(0, truncateAfter) + "\n\n" + TRUNCATED_MESSAGE;
}

function makeOutput(
  fileContent: string,
  fileDescriptor: string,
  initLine: number = 1,
): string {
  const truncatedContent = maybeTruncate(fileContent);
  const lines = truncatedContent.split("\n");
  const numberedLines = lines.map(
    (line, i) => `${(i + initLine).toString().padStart(6)}	${line}`,
  );
  return `Here's the result of running \`cat -n\` on ${fileDescriptor}:\n${numberedLines.join("\n")}\n`;
}

export const searchReplaceEditorImpl: ToolImpl = async (args, extras) => {
  let command: string;
  let path: string;

  try {
    command = getStringArg(args, "command");
    path = getStringArg(args, "path");
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return [
      {
        name: "Search Replace Editor Failed",
        description: "Search Replace Editor Failed: Invalid arguments provided",
        content: errorMessage,
        type: "error",
      },
    ];
  }

  // Resolve the file path
  const resolvedFilepath = await resolveRelativePathInDir(path, extras.ide);

  try {
    switch (command) {
      case "view":
        return await handleView(resolvedFilepath, args, extras);
      case "create":
        return await handleCreate(resolvedFilepath, args, extras);
      case "str_replace":
        return await handleStrReplace(resolvedFilepath, args, extras);
      case "insert":
        return await handleInsert(resolvedFilepath, args, extras);
      case "undo_edit":
        return await handleUndoEdit(resolvedFilepath, args, extras);
      default:
        return [
          {
            name: "Search Replace Editor Failed",
            description: `Unrecognized command ${command}`,
            content: `Unrecognized command ${command}. The allowed commands are: "view", "create", "str_replace", "insert", "undo_edit"`,
            type: "error",
          },
        ];
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return [
      {
        name: "Search Replace Editor Failed",
        description: `Failed to execute ${command} on ${path}`,
        content: `Failed to execute ${command}: ${errorMessage}`,
        type: "error",
      },
    ];
  }
};

async function handleView(
  resolvedFilepath: string | undefined,
  args: any,
  extras: ToolExtras,
): Promise<ContextItem[]> {
  if (!resolvedFilepath) {
    return [
      {
        name: "View Failed",
        description: `Path ${args.path} does not exist`,
        content: `Path ${args.path} does not exist. Please provide a valid path.`,
        type: "error",
      },
    ];
  }

  // Check if it's a directory
  try {
    const contents = await extras.ide.listDir(resolvedFilepath);
    // If listDir succeeds, it's a directory

    const fileList = contents
      .map(([path, type]) => `${path}${type === 2 ? "/" : ""}`)
      .join("\n");
    return [
      {
        name: "Directory Contents",
        description: `Contents of ${args.path}`,
        content: `Here's the files and directories up to 2 levels deep in ${args.path}, excluding hidden items:\n${fileList}\n`,
        type: "success",
        uri: {
          type: "file",
          value: resolvedFilepath,
        },
      },
    ];
  } catch (error) {
    // If listDir fails, assume it's a file and try to read it
  }

  // Read file content
  const fileContent = await extras.ide.readFile(resolvedFilepath);
  const viewRange = args.view_range;

  if (viewRange && Array.isArray(viewRange) && viewRange.length === 2) {
    const [startLine, endLine] = viewRange;
    const lines = fileContent.split("\n");
    const totalLines = lines.length;

    if (startLine < 1 || startLine > totalLines) {
      return [
        {
          name: "View Failed",
          description: `Invalid view_range`,
          content: `Invalid view_range: ${viewRange}. Start line ${startLine} should be within the range [1, ${totalLines}]`,
          type: "error",
        },
      ];
    }

    const actualEndLine =
      endLine === -1 ? totalLines : Math.min(endLine, totalLines);
    if (actualEndLine < startLine) {
      return [
        {
          name: "View Failed",
          description: `Invalid view_range`,
          content: `Invalid view_range: ${viewRange}. End line ${actualEndLine} should be >= start line ${startLine}`,
          type: "error",
        },
      ];
    }

    const selectedLines = lines.slice(startLine - 1, actualEndLine);
    const selectedContent = selectedLines.join("\n");

    return [
      {
        name: "File View",
        description: `Lines ${startLine}-${actualEndLine} of ${args.path}`,
        content: makeOutput(
          selectedContent,
          `${args.path} (lines ${startLine}-${actualEndLine})`,
          startLine,
        ),
        type: "success",
        uri: {
          type: "file",
          value: resolvedFilepath,
        },
      },
    ];
  }

  return [
    {
      name: "File View",
      description: `Contents of ${args.path}`,
      content: makeOutput(fileContent, args.path),
      type: "success",
      uri: {
        type: "file",
        value: resolvedFilepath,
      },
    },
  ];
}

async function handleCreate(
  resolvedFilepath: string | undefined,
  args: any,
  extras: ToolExtras,
): Promise<ContextItem[]> {
  const fileText = args.file_text;
  if (fileText === undefined) {
    return [
      {
        name: `Create Failed for ${resolvedFilepath}`,
        description: "Parameter `file_text` is required for command: create",
        content: "Parameter `file_text` is required for command: create",
        type: "error",
      },
    ];
  }

  // Use inferResolvedUriFromRelativePath for new file creation
  const newFileUri = await inferResolvedUriFromRelativePath(
    args.path,
    extras.ide,
  );
  if (!newFileUri) {
    return [
      {
        name: "Create Failed",
        description: `Failed to resolve path: ${args.path}`,
        content: `Failed to resolve path: ${args.path}. Please verify the path is correct and relative to the workspace root.`,
        type: "error",
      },
    ];
  }

  // Check if file already exists
  const exists = await extras.ide.fileExists(newFileUri);
  if (exists) {
    return [
      {
        name: "Create Failed",
        description: `File already exists at: ${args.path}`,
        content: `File already exists at: ${args.path}. Cannot overwrite files using command \`create\`.`,
        type: "error",
      },
    ];
  }

  await extras.ide.writeFile(newFileUri, fileText);
  await extras.ide.saveFile(newFileUri);

  // Initialize file history with create operation
  const createOperation: FileOperation = {
    command: "create",
    path: newFileUri,
    content: fileText,
    fileDescriptor: args.path,
  };
  fileHistory.set(newFileUri, [createOperation]);

  // Refresh codebase index if available
  if (extras.codeBaseIndexer) {
    void extras.codeBaseIndexer?.refreshCodebaseIndexFiles([newFileUri]);
  }

  return [
    {
      name: "File Created",
      description: `File created successfully at: ${args.path}`,
      content: `File created successfully at: ${args.path}`,
      type: "success",
      uri: {
        type: "file",
        value: newFileUri,
      },
    },
  ];
}

async function handleStrReplace(
  resolvedFilepath: string | undefined,
  args: any,
  extras: ToolExtras,
): Promise<ContextItem[]> {
  if (!resolvedFilepath) {
    return [
      {
        name: "String Replace Failed",
        description: `File ${args.path} does not exist`,
        content: `File ${args.path} does not exist`,
        type: "error",
      },
    ];
  }

  const oldStr = args.old_str;
  const newStr = args.new_str || "";

  if (oldStr === undefined) {
    return [
      {
        name: "String Replace Failed",
        description: "Parameter `old_str` is required for command: str_replace",
        content: "Parameter `old_str` is required for command: str_replace",
        type: "error",
      },
    ];
  }

  // Read current file content
  const fileContent = await extras.ide.readFile(resolvedFilepath);

  // Check if old_str exists and is unique
  const occurrences = (
    fileContent.match(
      new RegExp(oldStr.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"), "g"),
    ) || []
  ).length;

  if (occurrences === 0) {
    return [
      {
        name: "String Replace Failed",
        description: `No replacement was performed, old_str did not appear verbatim in ${args.path}`,
        content: `No replacement was performed, old_str \`${oldStr}\` did not appear verbatim in ${args.path}.`,
        type: "error",
      },
    ];
  }

  if (occurrences > 1) {
    const lines = fileContent.split("\n");
    const matchingLines = lines
      .map((line, index) => (line.includes(oldStr) ? index + 1 : null))
      .filter((lineNum) => lineNum !== null);

    return [
      {
        name: "String Replace Failed",
        description: `Multiple occurrences of old_str found`,
        content: `No replacement was performed. Multiple occurrences of old_str \`${oldStr}\` in lines ${matchingLines.join(", ")}. Please ensure it is unique`,
        type: "error",
      },
    ];
  }

  if (newStr === oldStr) {
    return [
      {
        name: "String Replace Failed",
        description: `No replacement was performed, old_str is the same as new_str`,
        content: `No replacement was performed, old_str \`${oldStr}\` is the same as new_str \`${newStr}\`.`,
        type: "error",
      },
    ];
  }

  // Save current operation to history before making changes
  const history = fileHistory.get(resolvedFilepath) || [];
  const strReplaceOperation: FileOperation = {
    command: "str_replace",
    path: resolvedFilepath,
    content: fileContent,
    fileDescriptor: args.path,
  };
  history.push(strReplaceOperation);
  fileHistory.set(resolvedFilepath, history);

  // Perform replacement
  const newFileContent = fileContent.replace(oldStr, newStr);

  await extras.ide.writeFile(resolvedFilepath, newFileContent);
  await extras.ide.saveFile(resolvedFilepath);

  // Refresh codebase index if available
  if (extras.codeBaseIndexer) {
    void extras.codeBaseIndexer?.refreshCodebaseIndexFiles([resolvedFilepath]);
  }

  // Create snippet showing the change
  const replacementLine = fileContent.split(oldStr)[0].split("\n").length;
  const startLine = Math.max(1, replacementLine - SNIPPET_LINES);
  const endLine = Math.min(
    replacementLine + SNIPPET_LINES + newStr.split("\n").length,
    newFileContent.split("\n").length,
  );

  const snippetLines = newFileContent.split("\n").slice(startLine - 1, endLine);
  const snippet = snippetLines.join("\n");

  return [
    {
      name: "String Replace Completed",
      description: `The file ${args.path} has been edited`,
      content: `The file ${args.path} has been edited. ${makeOutput(snippet, `a snippet of ${args.path}`, startLine)}Review the changes and make sure they are as expected. Edit the file again if necessary.`,
      type: "success",
      uri: {
        type: "file",
        value: resolvedFilepath,
      },
    },
  ];
}

async function handleInsert(
  resolvedFilepath: string | undefined,
  args: any,
  extras: ToolExtras,
): Promise<ContextItem[]> {
  if (!resolvedFilepath) {
    return [
      {
        name: "Insert Failed",
        description: `File ${args.path} does not exist`,
        content: `File ${args.path} does not exist`,
        type: "error",
      },
    ];
  }

  const insertLine = args.insert_line;
  const newStr = args.new_str;

  if (insertLine === undefined) {
    return [
      {
        name: "Insert Failed",
        description: "Parameter `insert_line` is required for command: insert",
        content: "Parameter `insert_line` is required for command: insert",
        type: "error",
      },
    ];
  }

  if (newStr === undefined) {
    return [
      {
        name: "Insert Failed",
        description: "Parameter `new_str` is required for command: insert",
        content: "Parameter `new_str` is required for command: insert",
        type: "error",
      },
    ];
  }

  // Read current file content
  const fileContent = await extras.ide.readFile(resolvedFilepath);
  const lines = fileContent.split("\n");
  const totalLines = lines.length;

  if (insertLine < 0 || insertLine > totalLines) {
    return [
      {
        name: "Insert Failed",
        description: `Invalid insert_line parameter: ${insertLine}`,
        content: `Invalid \`insert_line\` parameter: ${insertLine}. It should be within the range [0, ${totalLines}]`,
        type: "error",
      },
    ];
  }

  // Save current operation to history before making changes
  const history = fileHistory.get(resolvedFilepath) || [];
  const insertOperation: FileOperation = {
    command: "insert",
    path: resolvedFilepath,
    content: fileContent,
    fileDescriptor: args.path,
    insertLine: insertLine,
  };
  history.push(insertOperation);
  fileHistory.set(resolvedFilepath, history);

  // Insert new content
  const newStrLines = newStr.split("\n");
  const newFileLines = [
    ...lines.slice(0, insertLine),
    ...newStrLines,
    ...lines.slice(insertLine),
  ];
  const newFileContent = newFileLines.join("\n");

  await extras.ide.writeFile(resolvedFilepath, newFileContent);
  await extras.ide.saveFile(resolvedFilepath);

  // Refresh codebase index if available
  if (extras.codeBaseIndexer) {
    void extras.codeBaseIndexer?.refreshCodebaseIndexFiles([resolvedFilepath]);
  }

  // Create snippet showing the insertion
  const snippetStartLine = Math.max(1, insertLine - SNIPPET_LINES + 1);
  const snippetEndLine = Math.min(
    insertLine + SNIPPET_LINES + newStrLines.length,
    newFileLines.length,
  );
  const snippetLines = newFileLines.slice(snippetStartLine - 1, snippetEndLine);
  const snippet = snippetLines.join("\n");

  return [
    {
      name: "Insert Completed",
      description: `The file ${args.path} has been edited`,
      content: `The file ${args.path} has been edited. ${makeOutput(snippet, `a snippet of the edited file`, snippetStartLine)}Review the changes and make sure they are as expected (correct indentation, no duplicate lines, etc.). Edit the file again if necessary.`,
      type: "success",
      uri: {
        type: "file",
        value: resolvedFilepath,
      },
    },
  ];
}

async function handleUndoEdit(
  resolvedFilepath: string | undefined,
  args: any,
  extras: ToolExtras,
): Promise<ContextItem[]> {
  if (!resolvedFilepath) {
    return [
      {
        name: "Undo Edit Failed",
        description: `File ${args.path} does not exist`,
        content: `File ${args.path} does not exist`,
        type: "error",
      },
    ];
  }

  const history = fileHistory.get(resolvedFilepath) || [];
  if (history.length === 0) {
    return [
      {
        name: "Undo Edit Failed",
        description: `No edit history found for ${args.path}`,
        content: `No edit history found for ${args.path}.`,
        type: "error",
      },
    ];
  }

  // Restore the last version from the last operation
  const lastOperation = history.pop()!;
  const previousContent = lastOperation.content;
  fileHistory.set(resolvedFilepath, history);

  await extras.ide.writeFile(resolvedFilepath, previousContent);
  await extras.ide.saveFile(resolvedFilepath);

  // Refresh codebase index if available
  if (extras.codeBaseIndexer) {
    void extras.codeBaseIndexer?.refreshCodebaseIndexFiles([resolvedFilepath]);
  }

  return [
    {
      name: "Undo Edit Completed",
      description: `Last edit to ${args.path} undone successfully`,
      content: `Last edit to ${args.path} undone successfully. ${makeOutput(previousContent, args.path)}`,
      type: "success",
      uri: {
        type: "file",
        value: resolvedFilepath,
      },
    },
  ];
}
