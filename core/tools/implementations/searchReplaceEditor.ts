import { resolveRelativePathInDir } from "../../util/ideUtils";
import { ToolImpl } from ".";
import { getStringArg } from "../parseArgs";
import { handleCreate, handleDelete, handleInsert, handleStrReplace, handleUndoEdit, handleView, } from "./editor";

export const searchReplaceEditorImpl: ToolImpl = async (args, extras) => {
  let command: string;
  let path: string;

  try {
    command = getStringArg(args, "command");
    path = getStringArg(args, "path");
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return [
      {
        name: "Search Replace Editor Failed",
        description: "Search Replace Editor Failed: Invalid arguments provided",
        content: errorMessage,
        type: "error",
      },
    ];
  }

  // Resolve the file path
  const resolvedFilepath = await resolveRelativePathInDir(path, extras.ide);

  try {
    switch (command) {
      case "view":
        return await handleView(resolvedFilepath, args, extras);
      case "create":
        return await handleCreate(resolvedFilepath, args, extras);
      case "str_replace":
        return await handleStrReplace(resolvedFilepath, args, extras);
      case "insert":
        return await handleInsert(resolvedFilepath, args, extras);
      case "undo_edit":
        return await handleUndoEdit(resolvedFilepath, args, extras);
      case "delete":
        return await handleDelete(resolvedFilepath, args, extras);
      default:
        return [
          {
            name: "Search Replace Editor Failed",
            description: `Unrecognized command ${command}`,
            content: `Unrecognized command ${command}. The allowed commands are: "view", "create", "str_replace", "insert", "undo_edit", "delete"`,
            type: "error",
          },
        ];
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return [
      {
        name: "Search Replace Editor Failed",
        description: `Failed to execute ${command} on ${path}`,
        content: `Failed to execute ${command}: ${errorMessage}`,
        type: "error",
      },
    ];
  }
};
