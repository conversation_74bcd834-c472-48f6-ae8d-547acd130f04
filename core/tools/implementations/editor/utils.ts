const TRUNCATED_MESSAGE =
  "To save on context only part of this file has been shown to you. You should retry this tool after you have searched inside the file with `grep -n` in order to find the line numbers of what you are looking for.";
const MAX_RESPONSE_LEN = 16000;
export const SNIPPET_LINES = 4;

export function maybeTruncate(
  content: string,
  truncateAfter: number = MAX_RESPONSE_LEN,
): string {
  return content.length <= truncateAfter
    ? content
    : content.substring(0, truncateAfter) + "\n\n" + TRUNCATED_MESSAGE;
}

export function makeOutput(
  fileContent: string,
  fileDescriptor: string,
  initLine: number = 1,
): string {
  const truncatedContent = maybeTruncate(fileContent);
  const lines = truncatedContent.split("\n");
  const numberedLines = lines.map(
    (line, i) => `${(i + initLine).toString().padStart(6)}	${line}`,
  );
  return `Here's the result of running \`cat -n\` on ${fileDescriptor}:\n${numberedLines.join("\n")}\n`;
}
