import type { ContextItem, ToolExtras } from "../../..";
import { FileOperation, fileHistory } from "./types";
import { makeOutput, SNIPPET_LINES } from "./utils";

export async function handleInsert(
  resolvedFilepath: string | undefined,
  args: any,
  extras: ToolExtras,
): Promise<ContextItem[]> {
  if (!resolvedFilepath) {
    return [
      {
        name: "Insert Failed",
        description: `File ${args.path} does not exist`,
        content: `File ${args.path} does not exist`,
        type: "error",
      },
    ];
  }

  const insertLine = args.insert_line;
  const newStr = args.new_str;

  if (insertLine === undefined) {
    return [
      {
        name: "Insert Failed",
        description: "Parameter `insert_line` is required for command: insert",
        content: "Parameter `insert_line` is required for command: insert",
        type: "error",
      },
    ];
  }

  if (newStr === undefined) {
    return [
      {
        name: "Insert Failed",
        description: "Parameter `new_str` is required for command: insert",
        content: "Parameter `new_str` is required for command: insert",
        type: "error",
      },
    ];
  }

  // Read current file content
  const fileContent = await extras.ide.readFile(resolvedFilepath);
  const lines = fileContent.split("\n");
  const totalLines = lines.length;

  if (insertLine < 0 || insertLine > totalLines) {
    return [
      {
        name: "Insert Failed",
        description: `Invalid insert_line parameter: ${insertLine}`,
        content: `Invalid \`insert_line\` parameter: ${insertLine}. It should be within the range [0, ${totalLines}]`,
        type: "error",
      },
    ];
  }

  // Save current operation to history before making changes
  const history = fileHistory.get(resolvedFilepath) || [];
  const insertOperation: FileOperation = {
    command: "insert",
    path: resolvedFilepath,
    content: fileContent,
    fileDescriptor: args.path,
    insertLine: insertLine,
  };
  history.push(insertOperation);
  fileHistory.set(resolvedFilepath, history);

  // Insert new content
  const newStrLines = newStr.split("\n");
  const newFileLines = [
    ...lines.slice(0, insertLine),
    ...newStrLines,
    ...lines.slice(insertLine),
  ];
  const newFileContent = newFileLines.join("\n");

  await extras.ide.writeFile(resolvedFilepath, newFileContent);
  await extras.ide.saveFile(resolvedFilepath);

  // Refresh codebase index if available
  if (extras.codeBaseIndexer) {
    void extras.codeBaseIndexer?.refreshCodebaseIndexFiles([resolvedFilepath]);
  }

  // Create snippet showing the insertion
  const snippetStartLine = Math.max(1, insertLine - SNIPPET_LINES + 1);
  const snippetEndLine = Math.min(
    insertLine + SNIPPET_LINES + newStrLines.length,
    newFileLines.length,
  );
  const snippetLines = newFileLines.slice(snippetStartLine - 1, snippetEndLine);
  const snippet = snippetLines.join("\n");

  return [
    {
      name: "Insert Completed",
      description: `The file ${args.path} has been edited`,
      content: `The file ${args.path} has been edited. ${makeOutput(snippet, `a snippet of the edited file`, snippetStartLine)}Review the changes and make sure they are as expected (correct indentation, no duplicate lines, etc.). Edit the file again if necessary.`,
      type: "success",
      uri: {
        type: "file",
        value: resolvedFilepath,
      },
    },
  ];
}
