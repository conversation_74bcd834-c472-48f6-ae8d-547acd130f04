import type { ContextItem, ToolEx<PERSON> } from "../../..";
import { fileHistory } from "./types";

export async function handleDelete(
  resolvedFilepath: string | undefined,
  args: any,
  extras: ToolExtras,
): Promise<ContextItem[]> {
  if (!resolvedFilepath) {
    return [
      {
        name: "Delete Failed",
        description: `File ${args.path} does not exist`,
        content: `File ${args.path} does not exist`,
        type: "error",
      },
    ];
  }

  try {
    // Check if file exists before attempting to delete
    const fileExists = await extras.ide.fileExists(resolvedFilepath);
    if (!fileExists) {
      return [
        {
          name: "Delete Failed",
          description: `File ${args.path} does not exist`,
          content: `File ${args.path} does not exist`,
          type: "error",
        },
      ];
    }

    // Delete the file using IDE's deleteFile method
    const deleteResult = await extras.ide.deleteFile(resolvedFilepath);

    if (!deleteResult) {
      return [
        {
          name: "Delete Failed",
          description: `Failed to delete file ${args.path}`,
          content: `Failed to delete file ${args.path}. The file may be in use or you may not have permission to delete it.`,
          type: "error",
        },
      ];
    }

    // Clear file history for the deleted file
    fileHistory.delete(resolvedFilepath);

    // Refresh codebase index if available
    if (extras.codeBaseIndexer) {
      void extras.codeBaseIndexer?.refreshCodebaseIndexFiles([resolvedFilepath]);
    }

    return [
      {
        name: "Delete Completed",
        description: `File ${args.path} deleted successfully`,
        content: `File ${args.path} has been deleted successfully.`,
        type: "success",
      },
    ];
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return [
      {
        name: "Delete Failed",
        description: `Error deleting file ${args.path}`,
        content: `Error deleting file ${args.path}: ${errorMessage}`,
        type: "error",
      },
    ];
  }
}
