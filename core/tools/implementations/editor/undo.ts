import type { ContextItem, ToolExtras } from "../../..";
import { fileHistory } from "./types";
import { makeOutput } from "./utils";

export async function handleUndoEdit(
  resolvedFilepath: string | undefined,
  args: any,
  extras: ToolExtras,
): Promise<ContextItem[]> {
  if (!resolvedFilepath) {
    return [
      {
        name: "Undo Edit Failed",
        description: `File ${args.path} does not exist`,
        content: `File ${args.path} does not exist`,
        type: "error",
      },
    ];
  }

  const history = fileHistory.get(resolvedFilepath) || [];
  if (history.length === 0) {
    return [
      {
        name: "Undo Edit Failed",
        description: `No edit history found for ${args.path}`,
        content: `No edit history found for ${args.path}.`,
        type: "error",
      },
    ];
  }

  // Restore the last version from the last operation
  const lastOperation = history.pop()!;
  const previousContent = lastOperation.content;
  fileHistory.set(resolvedFilepath, history);

  await extras.ide.writeFile(resolvedFilepath, previousContent);
  await extras.ide.saveFile(resolvedFilepath);

  // Refresh codebase index if available
  if (extras.codeBaseIndexer) {
    void extras.codeBaseIndexer?.refreshCodebaseIndexFiles([resolvedFilepath]);
  }

  return [
    {
      name: "Undo Edit Completed",
      description: `Last edit to ${args.path} undone successfully`,
      content: `Last edit to ${args.path} undone successfully. ${makeOutput(previousContent, args.path)}`,
      type: "success",
      uri: {
        type: "file",
        value: resolvedFilepath,
      },
    },
  ];
}
