import {
  inferResolvedUriFromRelativePath,
} from "../../../util/ideUtils";
import type { ContextItem, ToolExtras } from "../../..";
import { FileOperation, fileHistory } from "./types";

export async function handleCreate(
  resolvedFilepath: string | undefined,
  args: any,
  extras: ToolExtras,
): Promise<ContextItem[]> {
  const fileText = args.file_text;
  if (fileText === undefined) {
    return [
      {
        name: `Create Failed for ${resolvedFilepath}`,
        description: "Parameter `file_text` is required for command: create",
        content: "Parameter `file_text` is required for command: create",
        type: "error",
      },
    ];
  }

  // Use inferResolvedUriFromRelativePath for new file creation
  const newFileUri = await inferResolvedUriFromRelativePath(
    args.path,
    extras.ide,
  );
  if (!newFileUri) {
    return [
      {
        name: "Create Failed",
        description: `Failed to resolve path: ${args.path}`,
        content: `Failed to resolve path: ${args.path}. Please verify the path is correct and relative to the workspace root.`,
        type: "error",
      },
    ];
  }

  // Check if file already exists
  const exists = await extras.ide.fileExists(newFileUri);
  if (exists) {
    return [
      {
        name: "Create Failed",
        description: `File already exists at: ${args.path}`,
        content: `File already exists at: ${args.path}. Cannot overwrite files using command \`create\`.`,
        type: "error",
      },
    ];
  }

  await extras.ide.writeFile(newFileUri, fileText);
  await extras.ide.saveFile(newFileUri);

  // Initialize file history with create operation
  const createOperation: FileOperation = {
    command: "create",
    path: newFileUri,
    content: fileText,
    fileDescriptor: args.path,
  };
  fileHistory.set(newFileUri, [createOperation]);

  // Refresh codebase index if available
  if (extras.codeBaseIndexer) {
    void extras.codeBaseIndexer?.refreshCodebaseIndexFiles([newFileUri]);
  }

  return [
    {
      name: "File Created",
      description: `File created successfully at: ${args.path}`,
      content: `File created successfully at: ${args.path}`,
      type: "success",
      uri: {
        type: "file",
        value: newFileUri,
      },
    },
  ];
}
